import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/model/others/billed_item_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class AddSaleBilledItemResponse {
  final LineItemDetailModel? billedItem;

  final bool newFlag;
  final bool backFlag;
  final bool deleteFlag;

  AddSaleBilledItemResponse(
      {this.billedItem,
      this.newFlag = false,
      this.backFlag = false,
      this.deleteFlag = false});
}

class AddEditSaleReturnBillItemController extends GetxController {
  final String tag = "AddEditSaleReturnBillItemController";
  UnitListController unitListController = Get.find();

  ItemRepository itemRepository = ItemRepository();

  var _billedItem = LineItemDetailModel().obs;
  var _editFlag = false.obs;

  LineItemDetailModel get billedItem => _billedItem.value;
  bool get editFlag => _editFlag.value;
  final formKey = GlobalKey<FormState>();

  TextEditingController itemNameCtrl = TextEditingController();
  ItemModel selectedItem = ItemModel();
  TextEditingController qtyCtrl = TextEditingController();
  TextEditingController rateCtrl = TextEditingController();
  TextEditingController grossAmountCtrl = TextEditingController();
  TextEditingController discountPercentageCtrl = TextEditingController();
  TextEditingController discountAmountCtrl = TextEditingController();
  TextEditingController netAmountCtrl = TextEditingController();
  BilledItemUnit selectedUnit = BilledItemUnit();
  List<BilledItemUnit> unitList = [];

  @override
  void onInit() async {
    super.onInit();
  }

  @override
  void dispose() {
    itemNameCtrl.dispose();
    qtyCtrl.dispose();
    rateCtrl.dispose();
    grossAmountCtrl.dispose();
    discountPercentageCtrl.dispose();
    discountAmountCtrl.dispose();
    netAmountCtrl.dispose();
    super.dispose();
  }

  init() async {}

  initEdit(LineItemDetailModel lineItem) async {
    _editFlag.value = true;

    ItemModel? item;
    if (null != lineItem.itemId) {
      item = await itemRepository.getItemById(lineItem.itemId ?? "");
    } else {
      item = ItemModel(itemName: lineItem.itemName);
    }

    itemOnSelectHandler(lineItem.itemId,
        item: item, unitID: lineItem.lineItemUnitId ?? "");

    if (null != lineItem.lineItemUnitId) {
      unitOnSelectHandler(lineItem.lineItemUnitId ?? "0.00");
    }

    qtyCtrl.text = lineItem.quantity.toString();
    rateCtrl.text = lineItem.pricePerUnit.toString();
    grossAmountCtrl.text = lineItem.grossAmount.toString();
    discountPercentageCtrl.text = lineItem.discountPercent.toString();
    discountAmountCtrl.text = lineItem.discountAmount.toString();
    netAmountCtrl.text = lineItem.totalAmount.toString();

    _billedItem.value = lineItem;
  }

  itemOnClearHandler() {
    itemNameCtrl.text = "";
  }

  onDiscountAmoutChange(val) {
    double disAmt = (parseDouble(val) ?? 0.00);
    billedItem.discountAmount = disAmt;
    sabkoMilaune(editorTag: 'dis_amt');
  }

  itemOnSelectHandler(itemID, {String? unitID, ItemModel? item}) {
    itemNameCtrl.text = item!.itemName ?? "";
    rateCtrl.text = ((item.itemSaleUnitPrice ?? 0.0) > 0.0
        ? item.itemSaleUnitPrice.toString()
        : "0.00");

    //assigning name
    billedItem.itemName = item.itemName;
    selectedItem = item;
    // Log.d("selected item ${item.toJson()}");

    unitList.clear();

    if (null != unitID) {
      UnitModel? baseUnit =
          unitListController.units.firstWhere((unit) => unit.unitId == unitID);
      unitList.add(new BilledItemUnit(
          unitId: baseUnit.unitId,
          unitName: baseUnit.unitName,
          unitShortName: baseUnit.unitShortName,
          conversionFactor: 0.00,
          unitType: "base"));

      unitOnSelectHandler(unitID);
    } else {
      unitList = unitListController.units.map((e) {
        return BilledItemUnit(
          unitId: e.unitId,
          unitName: e.unitName,
          unitShortName: e.unitShortName,
          conversionFactor: 0.0,
          unitType: "base",
        );
      }).toList();
      debugPrint(unitListController.units.toString());
      debugPrint(unitList.toString());
      unitOnSelectHandler(unitListController.units.first.unitId!);
    }

    if (null != item.alternateUnitId) {
      UnitModel altUnit = unitListController.units
          .firstWhere((unit) => unit.unitId == item.alternateUnitId);
      unitList.add(new BilledItemUnit(
          unitId: altUnit.unitId,
          unitName: altUnit.unitName,
          unitShortName: altUnit.unitShortName,
          conversionFactor: item.unitConversionFactor,
          unitType: "alt"));
    }
  }

  unitOnSelectHandler(String value) {
    // Log.d("unnit list ${unitList[0].unitId} foor value ${value}");

    selectedUnit = unitList.firstWhere((element) => element.unitId == value,
        orElse: () => BilledItemUnit());

    _billedItem.value.lineItemUnitId = value;
    _billedItem.value.lineItemUnitName = selectedUnit.unitShortName;
    _billedItem.value.lineItemUnitConversionFactor =
        selectedUnit.conversionFactor;

    _billedItem.refresh();

    if ("alt" == selectedUnit.unitType)
    // ignore: curly_braces_in_flow_control_structures
    if ((selectedItem.itemSaleUnitPrice ?? 0.0) > 0.0) {
      rateCtrl.text =
          (selectedItem.itemSaleUnitPrice! * selectedItem.unitConversionFactor!)
              .toStringAsFixed(2);
    } else {
      rateCtrl.text = "";
    }
    else {
      rateCtrl.text = ((selectedItem.itemSaleUnitPrice ?? 0.0) > 0.0
              ? selectedItem.itemSaleUnitPrice.toString()
              : null) ??
          "";
    }

    rateOnChangeHandler(rateCtrl.text);
  }

  qtyOnChangeHandler(value) {
    billedItem.quantity = (parseDouble(value) ?? 0.00);
    _billedItem.refresh();
    sabkoMilaune(editorTag: 'qty_amt');
  }

  rateOnChangeHandler(value) {
    print("this is rate");
    billedItem.pricePerUnit = (parseDouble(value) ?? 0.00);
    sabkoMilaune(editorTag: 'price_per_unit');
  }

  amountOnChangeHandler(value) {
    billedItem.grossAmount = (parseDouble(value) ?? 0.00);
    _billedItem.refresh();
    sabkoMilaune(editorTag: "gross_amt");
  }

  discountPercentOnChangeHandler(String value) {
    billedItem.discountPercent = (parseDouble(value) ?? 0.00);
    sabkoMilaune(editorTag: 'dis_percent');
  }

  sabkoMilaune({String? editorTag}) {
    if (editorTag == "gross_amt") {
      double discount = (billedItem.discountAmount ?? 0.00);
      double discountPer = (billedItem.discountPercent ?? 0.00);
      double netAmt = (billedItem.grossAmount! - discount);
      billedItem.discountAmount = discount;
      billedItem.discountPercent = discountPer;
      billedItem.totalAmount = netAmt;
      _billedItem.refresh();
      netAmountCtrl.text = netAmt.toStringAsFixed(2);
      double qty = (billedItem.quantity ?? 0.00);
      if (qty > 0) {
        double gross = (billedItem.grossAmount ?? 0.00);
        double pricePerUnit = gross / qty;
        billedItem.pricePerUnit = pricePerUnit;
        _billedItem.refresh();
        // rateCtrl.text = pricePerUnit.toStringAsFixed(2);
      }
    }
    if (editorTag == 'qty_amt') {
      double qty = (billedItem.quantity ?? 0.00);
      double rate = (billedItem.pricePerUnit ?? 0.00);
      double gross = (billedItem.grossAmount ?? 0.00);
      if (rate > 0.00) {
        gross = qty * rate;
        billedItem.grossAmount = gross;
        billedItem.totalAmount = gross;
        _billedItem.refresh();
        grossAmountCtrl.text = gross.toStringAsFixed(2);
        netAmountCtrl.text = gross.toStringAsFixed(2);
      }
    }
    if (editorTag == 'price_per_unit') {
      double price = (billedItem.pricePerUnit ?? 0.00);
      double qty = (billedItem.quantity ?? 0.00);
      double gross = price * qty;
      if (price > 0) {
        billedItem.grossAmount = gross;
        _billedItem.refresh();
        grossAmountCtrl.text = gross.toStringAsFixed(2);
      }
    }

    if (editorTag == 'dis_percent') {
      double disPer = (billedItem.discountPercent ?? 0.00);
      double gross = (billedItem.grossAmount ?? 0.00);
      double disAmt = gross * disPer * 0.01;
      billedItem.discountAmount = disAmt;
      discountAmountCtrl.text = disAmt.toStringAsFixed(2);
      double net = gross - disAmt;
      billedItem.totalAmount = net;
      netAmountCtrl.text = net.toStringAsFixed(2);
    }
    if (editorTag == 'dis_amt') {
      double disAmt = (billedItem.discountAmount ?? 0.00);
      double gross = (billedItem.grossAmount ?? 0.00);
      double dispercent = (disAmt / gross) * 100;
      billedItem.discountPercent = dispercent;
      discountPercentageCtrl.text = dispercent.toStringAsFixed(2);
      double net = gross - disAmt;
      billedItem.totalAmount = net;
      netAmountCtrl.text = net.toStringAsFixed(2);
    }

    if (billedItem.discountPercent != null) {
      double disPer = (billedItem.discountPercent ?? 0.00);
      double gross = (billedItem.grossAmount ?? 0.00);
      double disAmt = gross * disPer * 0.01;
      billedItem.discountAmount = disAmt;
      discountAmountCtrl.text = disAmt.toStringAsFixed(2);
      double net = gross - disAmt;
      billedItem.totalAmount = net;
      netAmountCtrl.text = net.toStringAsFixed(2);
    }
  }
}
